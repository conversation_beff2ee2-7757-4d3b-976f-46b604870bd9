import cv2
import numpy as np

imgs_paths = [
    r"C:/Users/<USER>/Downloads/A.jfif",
    r"C:/Users/<USER>/Downloads/B.jfif",
    r"C:/Users/<USER>/Downloads/C.jfif",
    r"C:/Users/<USER>/Downloads/D.jfif"
]

imgs = [cv2.imread(path).astype(np.float32) / 255.0 for path in imgs_paths]
scale_percent = 40
new_w = int(imgs[0].shape[1] * scale_percent / 100)
new_h = int(imgs[0].shape[0] * scale_percent / 100)
imgs = [cv2.resize(img, (new_w, new_h)) for img in imgs]

def compute_contrast_weight(img):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    lap = cv2.Laplacian(gray, cv2.CV_32F)
    return np.abs(lap)

def compute_saturation_weight(img):
    return np.std(img, axis=2)

def compute_exposure_weight(img, sigma=0.3):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    return np.exp(-0.5 * ((gray - 0.5) ** 2) / (sigma ** 2))

alpha, beta, gamma = 0.2, 0.3, 0.5
weights = []
for img in imgs:
    C = compute_contrast_weight(img)
    S = compute_saturation_weight(img)
    E = compute_exposure_weight(img)
    W = (C ** alpha) * (S ** beta) * (E ** gamma)
    weights.append(W + 1e-12)

weights_sum = np.sum(weights, axis=0)
weights = [w / weights_sum for w in weights]

hdr = np.zeros_like(imgs[0])
for img, w in zip(imgs, weights):
    w3 = cv2.merge([w, w, w])
    hdr += img * w3

hdr_8bit = np.clip(hdr * 255, 0, 255).astype(np.uint8)
display_scale = 0.6
display_w = int(hdr_8bit.shape[1] * display_scale)
display_h = int(hdr_8bit.shape[0] * display_scale)
hdr_small = cv2.resize(hdr_8bit, (display_w, display_h))

cv2.imshow("HDR Exposure Fusion (Manual)", hdr_small)
cv2.waitKey(0)
cv2.destroyAllWindows()
