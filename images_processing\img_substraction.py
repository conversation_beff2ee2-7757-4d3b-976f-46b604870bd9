import cv2
import numpy as np

img1 = cv2.imread("C:/Users/<USER>/Downloads/noise.jpg", cv2.IMREAD_GRAYSCALE)
if img1 is None:
    print("Không tìm thấy ảnh.")
else:
    img1 = cv2.resize(img1, (400, 300))
    rows, cols = img1.shape
    M = np.float32([[1, 0, 5], [0, 1, 5]])
    img2 = cv2.warpAffine(img1, M, (cols, rows))
    diff = cv2.subtract(img2, img1)
    min_val, max_val = np.min(diff), np.max(diff)
    enhanced_diff = ((diff - min_val) / (max_val - min_val) * 255).astype(np.uint8) if max_val > min_val else np.zeros_like(diff)
    cv2.imshow("Original Image", img1)
    cv2.imshow("Shifted Image", img2)
    cv2.imshow("Difference Image", diff)
    cv2.imshow("Enhanced Difference Image", enhanced_diff)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
