import cv2
import numpy as np

img_path = 'C:/Users/<USER>/Downloads/noise.jpg'

img = cv2.imread(img_path).astype(np.float32)

N = 32
noisy_images = []

for i in range(N):
    noise = np.random.normal(0, 25, img.shape)  
    noisy_img = img + noise
    noisy_img = np.clip(noisy_img, 0, 255)
    noisy_images.append(noisy_img)

milestones = [1, 2, 8, 32]

for m in milestones:
    avg_img = np.mean(noisy_images[:m], axis=0)
    avg_img_uint8 = np.clip(avg_img, 0, 255).astype(np.uint8)
    
    cv2.imshow(f"Averaged with {m} images", avg_img_uint8)

first_noisy_uint8 = noisy_images[0].astype(np.uint8)
cv2.imshow("Original Noisy Image", first_noisy_uint8)

cv2.waitKey(0)
cv2.destroyAllWindows()
