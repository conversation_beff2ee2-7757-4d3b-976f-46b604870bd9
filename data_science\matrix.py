matrix = [[1, 1, 3], [4, 5, 6], [7, 8, 9]]
matrix2 = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]

def nhan_ma_tran(matrix, matrix2):
    for i in range(len(matrix)):
        for j in range(len(matrix2[0])):
            for k in range(len(matrix2)):
                matrix[i][j] += matrix[i][k] * matrix2[k][j]
    return matrix

def nhan_mot_so(matrix, n):
    for i in range(len(matrix)):
        for j in range(len(matrix[0])):
            matrix[i][j] *= n
    return matrix
def cong_ma_tran(matrix, matrix2):
    for i in range(len(matrix)):
        for j in range(len(matrix[0])):
            matrix[i][j] += matrix2[i][j]
    return matrix

print(nhan_ma_tran(matrix, matrix2))
print(nhan_mot_so(matrix, 2))
print(cong_ma_tran(matrix, matrix2))