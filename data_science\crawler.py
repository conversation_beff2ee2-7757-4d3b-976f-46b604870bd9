import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from collections import deque
import time

def crawl_admission_info(base_url, max_pages=50):
    headers = {"User-Agent": "Mozilla/5.0"}
    keywords = ["tuyển sinh", "trúng tuyển", "xét tuyển"]

    visited = set()
    queue = deque([base_url])
    admission_links = []

    while queue and len(visited) < max_pages:
        url = queue.popleft()
        if url in visited:
            continue
        visited.add(url)

        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, "html.parser")
        except Exception as e:
            print(f"Không thể truy cập {url} → {e}")
            continue

        for a_tag in soup.find_all("a", href=True):
            href = urljoin(url, a_tag["href"])
            text = a_tag.get_text(strip=True).lower()

            if urlparse(href).netloc != urlparse(base_url).netloc:
                continue

            if href not in visited and href not in queue:
                queue.append(href)

            if any(keyword in text for keyword in keywords):
                admission_links.append({
                    "title": text,
                    "href": href
                })

        time.sleep(0.5)

    data_2025 = []
    data_other = []

    for item in admission_links:
        link = item["href"]
        try:
            sub_resp = requests.get(link, headers=headers, timeout=10)
            sub_resp.raise_for_status()
            sub_soup = BeautifulSoup(sub_resp.text, "html.parser")

            article_title = sub_soup.find("title").get_text(strip=True) if sub_soup.find("title") else item["title"]
            paragraphs = [p.get_text(strip=True) for p in sub_soup.find_all("p")]
            content = "\n".join(paragraphs) if paragraphs else "Không tìm thấy nội dung."

            image_links = []
            for img in sub_soup.find_all("img", src=True):
                img_url = urljoin(link, img["src"])
                image_links.append(img_url)

            if "2025" in item["title"] or "2025" in article_title:
                data_2025.append((article_title, link, content, image_links))
            else:
                data_other.append((article_title, link, content, image_links))

        except Exception as e:
            print(f"Không thể crawl link: {link} → {e}")

    os.makedirs("2025", exist_ok=True)
    os.makedirs("other_years", exist_ok=True)
    os.makedirs("images", exist_ok=True)

    def save_articles(data, folder, prefix):
        for idx, (title, href, content, images) in enumerate(data, start=1):
            filename = f"{folder}/{prefix}_{idx}.txt"
            with open(filename, "w", encoding="utf-8") as f:
                f.write(f"Tiêu đề: {title}\nLink: {href}\n\n{content}\n\nHình ảnh:\n")
                for img_url in images:
                    f.write(f"{img_url}\n")
                    try:
                        img_data = requests.get(img_url, headers=headers, timeout=10).content
                        img_name = os.path.basename(img_url.split("?")[0])
                        img_path = f"images/{prefix}_{idx}_{img_name}"
                        with open(img_path, "wb") as img_file:
                            img_file.write(img_data)
                    except Exception as e:
                        print(f"Không tải được ảnh {img_url}: {e}")

    save_articles(data_2025, "2025", "admission_2025")
    save_articles(data_other, "other_years", "admission_other")

    print(f"Hoàn tất crawl {len(admission_links)} link bài tuyển sinh.")

url = "https://ptit.edu.vn/"
crawl_admission_info(url, max_pages=100)
