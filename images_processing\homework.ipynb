import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import cv2

peper_url = r'./images/peppersbin.sec'
lenabin_url = r'./images/lenabin.sec'

data1 = np.fromfile(peper_url, dtype=np.uint8)
arr1 =data1.reshape((256,256))
data2 = np.fromfile(lenabin_url, dtype=np.uint8)
arr2 =  data2.reshape((256,256))

new_image = np.zeros((256, 256), dtype=np.uint8)

for i in range(256):
    for j in range(256):
        if(j < 128):
            new_image[i][j] = arr1[i][j]
        else:
            new_image[i][j] = arr2[i][j]
            
new_image2 = np.zeros((256, 256), dtype=np.uint8)

for i in range(256):
    for j in range(256):
        if(j >= 128):
            new_image2[i][j] = arr1[i][j-128]
        else:
            new_image2[i][j] = arr2[i][128+j]

cv2.imshow('peperbin', arr1)
cv2.imshow('lenabin', arr2)
cv2.imshow('new_image', new_image)
cv2.imshow('new_image2', new_image2)
cv2.waitKey(0)
cv2.destroyAllWindows()

import cv2

lenagray_img = cv2.imread(r'./images/lenagray.jpg', 1)

cv2.imshow('lenagray', lenagray_img)
cv2.waitKey(0)
cv2.destroyAllWindows()

j2_img = 255-lenagray_img
cv2.imshow('j2_img', j2_img)
cv2.waitKey(0)
cv2.destroyAllWindows()

import cv2
import matplotlib.pyplot as plt

J1 = cv2.imread(r'./images/lena512color.jpg')  

J1_rgb = cv2.cvtColor(J1, cv2.COLOR_BGR2RGB)

plt.subplot(1, 2, 1)
plt.imshow(J1_rgb)
plt.title("Original Image J1")
plt.axis("off")

J2 = J1.copy()
J2[:,:,0] = J1[:,:,2]  
J2[:,:,1] = J1[:,:,0]  
J2[:,:,2] = J1[:,:,1]  

J2_rgb = cv2.cvtColor(J2, cv2.COLOR_BGR2RGB)

plt.subplot(1, 2, 2)
plt.imshow(J2_rgb)
plt.title("Modified Image J2")
plt.axis("off")

plt.show()

cv2.imwrite("lena_swapped.jpg", J2)


