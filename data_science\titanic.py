import pandas as pd
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.impute import SimpleImputer

df = pd.read_csv('titanic.csv')
print(df.head())
print(df.isnull().sum())
print("Số dòng trùng:", df.duplicated().sum())
imputer_age = SimpleImputer(strategy='mean')
df["age"] = imputer_age.fit_transform(df[['age']])

imputer_embarked = SimpleImputer(strategy='most_frequent')
df["embarked"] = imputer_embarked.fit_transform(df[['embarked']]).ravel()

imputer_fare = SimpleImputer(strategy="median")
df["fare"] = imputer_fare.fit_transform(df[["fare"]])

df.drop(["cabin", "boat", "body", "home.dest"], axis=1, inplace=True)

print(df.isnull().sum())

# Chuẩn hóa dữ liệu
minmax_cols = ['sibsp', 'parch']  
zscore_cols = ['age', 'fare']    

minmax_scaler = MinMaxScaler()
df[minmax_cols] = minmax_scaler.fit_transform(df[minmax_cols])

zscore_scaler = StandardScaler()
df[zscore_cols] = zscore_scaler.fit_transform(df[zscore_cols])

print(df.head())