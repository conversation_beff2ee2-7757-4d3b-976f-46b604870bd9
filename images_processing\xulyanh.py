import cv2
import numpy as np

url = "C:/Users/<USER>/Downloads/profile.jpg"
img = cv2.imread(url, 0)
if img is None:
    raise FileNotFoundError("<PERSON><PERSON><PERSON><PERSON> đọ<PERSON> được ảnh. <PERSON><PERSON><PERSON> tra lại đường dẫn.")

def reduce_bit(img, bit):
    return (img >> (8 - bit)) << (8 - bit)

def adjust_contrast(img, alpha=1.0, beta=0):
    return cv2.convertScaleAbs(img, alpha=alpha, beta=beta)

bit_level = [7, 6, 5, 4, 3, 2, 1]

cv2.imshow('Original', img)

for b in bit_level:
    reduced_img = reduce_bit(img, b)
    
    contrast_img = adjust_contrast(reduced_img, alpha=1.5, beta=0)
    
    cv2.imshow(f'{b} bit + contrast', contrast_img)

cv2.waitKey(0)
cv2.destroyAllWindows()
