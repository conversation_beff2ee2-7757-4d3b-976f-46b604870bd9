{"cells": [{"cell_type": "code", "execution_count": null, "id": "dfc9b002", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 <PERSON><PERSON> cào dữ liệu tweet...\n", "✅ <PERSON><PERSON> cào 50 tweet li<PERSON>n quan bầu cử tổng thống <PERSON>.\n", "📁 <PERSON><PERSON> lưu 12 tweet ỦNG HỘ vào: tweets_ung_ho.csv\n", "📁 <PERSON><PERSON> 26 tweet PHẢN ĐỐI vào: tweets_phan_doi.csv\n", "🎯 Hoàn tất!\n"]}], "source": ["import tweepy\n", "import pandas as pd\n", "from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer\n", "\n", "BEARER_TOKEN = \"AAAAAAAAAAAAAAAAAAAAAMVB3wEAAAAAQl1jU79AKpdOUD%2BJLmkxPouApIY%3D0RCveYc7iw4Mc5ADFWVBnQnMXStTod2NMb75UWIbXqtqwVftFd\"  # <-- Thay bằng token của bạn\n", "\n", "client = tweepy.Client(bearer_token=BEARER_TOKEN, wait_on_rate_limit=True)\n", "\n", "query = \"(presidential election OR US election OR <PERSON> OR <PERSON>iden OR Harris OR Democrats OR Republicans) lang:en\"\n", "max_tweets = 50  \n", "\n", "tweets = []\n", "for tweet in tweepy.Paginator(client.search_recent_tweets,\n", "                              query=query,\n", "                              tweet_fields=['created_at', 'text', 'author_id', 'lang'],\n", "                              max_results=100).flatten(limit=max_tweets):\n", "    tweets.append([tweet.created_at, tweet.author_id, tweet.text])\n", "\n", "df = pd.DataFrame(tweets, columns=[\"Date\", \"UserID\", \"Content\"])\n", "\n", "analyzer = SentimentIntensityAnalyzer()\n", "\n", "def analyze_sentiment(text):\n", "    score = analyzer.polarity_scores(text)\n", "    if score['compound'] >= 0.05:\n", "        return \"Ủng hộ\"\n", "    elif score['compound'] <= -0.05:\n", "        return \"Phản đối\"\n", "    else:\n", "        return \"Trung lập\"\n", "\n", "df[\"Sentiment\"] = df[\"Content\"].apply(analyze_sentiment)\n", "\n", "df_support = df[df[\"Sentiment\"] == \"Ủng hộ\"]\n", "df_oppose = df[df[\"Sentiment\"] == \"Phản đối\"]\n", "\n", "df_support.to_csv(\"tweets_ung_ho.csv\", index=False, encoding=\"utf-8-sig\")\n", "df_oppose.to_csv(\"tweets_phan_doi.csv\", index=False, encoding=\"utf-8-sig\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "cc811d8a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "imageprocessing", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}